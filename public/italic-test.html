<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中文斜体测试 - TipTap样式</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display",
                "SF Pro Text", "Segoe UI", "Helvetica Neue", "Roboto", "Inter",
                "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "SimSun",
                "Arial", sans-serif;
            padding: 20px;
            line-height: 1.6;
            background: #f5f5f5;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }

        /* 复制WysiwygEditor的斜体样式 */
        .tiptap-style em,
        .tiptap-style .italic-text {
            font-style: italic;
            font-synthesis: style;
            -webkit-font-synthesis: style;
            -moz-font-synthesis: style;
            -ms-font-synthesis: style;
        }

        .tiptap-style em,
        .tiptap-style .italic-text {
            display: inline;
            font-style: italic;
        }

        .tiptap-style em:not([data-native-italic]),
        .tiptap-style .italic-text:not([data-native-italic]) {
            transform: matrix(1, 0, -0.15, 1, 0, 0);
            display: inline-block;
            vertical-align: baseline;
        }

        @supports (font-synthesis: style) {

            .tiptap-style em,
            .tiptap-style .italic-text {
                font-synthesis: style;
                transform: none;
            }
        }

        @supports not (font-synthesis: style) {

            .tiptap-style em,
            .tiptap-style .italic-text {
                transform: matrix(1, 0, -0.2, 1, 0, 0);
                display: inline-block;
            }
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .before,
        .after {
            padding: 15px;
            border-radius: 5px;
        }

        .before {
            background: #ffebee;
            border: 2px solid #f44336;
        }

        .after {
            background: #e8f5e8;
            border: 2px solid #4caf50;
        }

        .before h3 {
            color: #d32f2f;
            margin-top: 0;
        }

        .after h3 {
            color: #2e7d32;
            margin-top: 0;
        }

        .default-italic em {
            font-style: italic;
        }

        code {
            background: rgba(0, 0, 0, 0.1);
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', monospace;
        }
    </style>
</head>

<body>
    <div class="test-container">
        <h1>🔧 中文斜体问题修复验证</h1>

        <div class="comparison">
            <div class="before">
                <h3>修复前（原始样式）</h3>
                <div class="default-italic">
                    <p>正常文本：<em>这是中文斜体测试 English italic test</em></p>
                    <p>混合文本：<em>中文Chinese英文English数字123</em></p>
                    <p>纯中文：<em>这是一段完全的中文文本内容</em></p>
                    <p>纯英文：<em>This is a complete English text content</em></p>
                </div>
            </div>

            <div class="after">
                <h3>修复后（TipTap样式）</h3>
                <div class="tiptap-style">
                    <p>正常文本：<em>这是中文斜体测试 English italic test</em></p>
                    <p>混合文本：<em>中文Chinese英文English数字123</em></p>
                    <p>纯中文：<em>这是一段完全的中文文本内容</em></p>
                    <p>纯英文：<em>This is a complete English text content</em></p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>技术实现说明</h2>
            <ul>
                <li><strong>font-synthesis: style</strong> - 现代浏览器自动为不支持斜体的字体合成倾斜效果</li>
                <li><strong>matrix变换</strong> - 为不支持font-synthesis的浏览器提供备用倾斜效果</li>
                <li><strong>中文字体支持</strong> - 添加了PingFang SC, Hiragino Sans GB等支持斜体的中文字体</li>
                <li><strong>渐进增强</strong> - 使用@supports查询提供不同浏览器的最佳体验</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>浏览器兼容性</h2>
            <p id="browser-support"></p>
        </div>

        <div class="test-section tiptap-style">
            <h2>实际编辑器效果预览</h2>
            <p>在便签编辑器中，你现在可以看到：</p>
            <p><em>中文斜体效果</em> 和 <em>English italic effect</em> 都能正常显示</p>
            <p>混合文本：<em>这里有中文 here is English 还有数字123</em></p>
        </div>
    </div>

    <script>
        // 检测浏览器支持情况
        function checkBrowserSupport() {
            const support = {
                fontSynthesis: CSS.supports('font-synthesis', 'style'),
                transform: CSS.supports('transform', 'matrix(1, 0, -0.2, 1, 0, 0)'),
                webkitFontSynthesis: CSS.supports('-webkit-font-synthesis', 'style')
            };

            const supportEl = document.getElementById('browser-support');
            let html = '<ul>';
            html += `<li>font-synthesis: ${support.fontSynthesis ? '✅ 支持' : '❌ 不支持'}</li>`;
            html += `<li>-webkit-font-synthesis: ${support.webkitFontSynthesis ? '✅ 支持' : '❌ 不支持'}</li>`;
            html += `<li>CSS transform matrix: ${support.transform ? '✅ 支持' : '❌ 不支持'}</li>`;
            html += '</ul>';

            const userAgent = navigator.userAgent;
            html += `<p><strong>当前浏览器:</strong> ${userAgent}</p>`;

            supportEl.innerHTML = html;
        }

        checkBrowserSupport();

        // 测试字体渲染
        console.log('🎨 中文斜体修复测试页面已加载');
        console.log('📊 请对比修复前后的斜体效果差异');
    </script>
</body>

</html>