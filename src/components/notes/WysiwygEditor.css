/* 所见即所得编辑器样式 */
.wysiwyg-editor {
  flex: 1; /* 关键：使用flex占满可用空间 */
  width: 100%;
  height: 100%; /* 确保占满父容器高度 */
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.2s ease;
  min-height: 0; /* 关键：允许flex子元素收缩 */
  overflow: hidden; /* 🎯 裁剪溢出内容，确保布局整洁 */
  padding: 0; /* 移除padding，让内部元素完全控制布局 */
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display",
    "SF Pro Text", "Segoe UI", "Helvetica Neue", "Roboto", "Inter",
    "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "SimSun", "Arial",
    sans-serif;
  font-size: var(--note-content-font-size, 14px);
  line-height: 1.5;
  color: #374151;
}

/* 修复EditorContent生成的包装div */
.wysiwyg-editor > div {
  flex: 1; /* 让EditorContent的包装div也使用flex */
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许收缩 */
  overflow: hidden; /* 确保不溢出 */
}

/* ✅ 简化状态控制样式 - 移除重复的disabled逻辑 */
/* 查看状态下的特殊样式 */
.wysiwyg-editor.viewing .ProseMirror {
  min-height: 50px;
  user-select: text;
  cursor: default;
}

/* 编辑状态下的样式 */
.wysiwyg-editor.editing .ProseMirror {
  user-select: auto;
  caret-color: #1890ff; /* 蓝色光标 */
}

/* ✅ 简化流式状态样式 */
.wysiwyg-editor.streaming .ProseMirror::after {
  content: "▋";
  color: #1890ff;
  font-weight: bold;
  animation: cursorBlink 1s infinite;
  margin-left: 2px;
}

.wysiwyg-editor-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: #6b7280;
  font-size: 14px;
}

/* ProseMirror 编辑器核心样式 - 支持通用类名和 tiptap 特定类名 */
.wysiwyg-editor .ProseMirror,
.wysiwyg-editor .tiptap.ProseMirror {
  flex: 1; /* 关键：使用flex占满可用空间 */
  height: 0; /* 关键：让flex完全控制高度 */
  max-height: 100%; /* 确保不超过容器高度 */
  margin: 0;
  outline: none;
  border: none;
  background: transparent;
  color: inherit;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  transition: all 0.2s ease;
  overflow-y: auto; /* 显示垂直滚动条 */
  overflow-x: hidden; /* 隐藏水平滚动条 */
  word-wrap: break-word;
  white-space: pre-wrap;
  box-sizing: border-box; /* 🎯 关键：padding计入总宽度 */
  resize: none;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0;

  /* 🎯 原生对称滚动条布局：由父容器提供内边距 */
  width: 100%; /* 占满容器宽度 */
  padding-left: 0; /* 移除左侧内边距，由父容器提供 */
  padding-right: 0; /* 移除右侧内边距，由父容器提供 */
}

/* 空编辑器占位符样式 */
.wysiwyg-editor .ProseMirror.is-editor-empty::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

/* 标题样式 */
.wysiwyg-editor .ProseMirror h1 {
  font-size: var(--note-h1-font-size, 18px);
  font-weight: 600;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.wysiwyg-editor .ProseMirror h2 {
  font-size: var(--note-h2-font-size, 16px);
  font-weight: 600;
  margin: 0 0 6px 0;
  line-height: 1.3;
}

.wysiwyg-editor .ProseMirror h3 {
  font-size: var(--note-h3-font-size, 15px);
  font-weight: 600;
  margin: 0 0 6px 0;
  line-height: 1.3;
}

.wysiwyg-editor .ProseMirror h4,
.wysiwyg-editor .ProseMirror h5,
.wysiwyg-editor .ProseMirror h6 {
  font-size: var(--note-content-font-size, 14px);
  font-weight: 600;
  margin: 0 0 4px 0;
  line-height: 1.3;
}

/* 段落样式 */
.wysiwyg-editor .ProseMirror p {
  margin: 0 0 8px 0;
  line-height: 1.5;
}

.wysiwyg-editor .ProseMirror p:last-child {
  margin-bottom: 0;
}

/* 文本格式样式 */
.wysiwyg-editor .ProseMirror strong {
  font-weight: 600;
}

/* 斜体样式 - 统一支持中英文，使用标准选择器 */
.wysiwyg-editor .ProseMirror em,
.wysiwyg-editor .ProseMirror i {
  font-style: italic;
  /* 关键：启用字体合成，让浏览器为不支持斜体的字体自动生成倾斜效果 */
  font-synthesis: style;
  -webkit-font-synthesis: style;
  -moz-font-synthesis: style;
}

/* 对于不支持 font-synthesis 的老版本浏览器，提供CSS变换后备方案 */
@supports not (font-synthesis: style) {
  .wysiwyg-editor .ProseMirror em,
  .wysiwyg-editor .ProseMirror i {
    display: inline-block;
    transform: skewX(-8deg);
    vertical-align: baseline;
  }
}

.wysiwyg-editor .ProseMirror code {
  background: rgba(0, 0, 0, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: "SF Mono", "Monaco", "Menlo", "Roboto Mono", "Consolas",
    "Liberation Mono", "Courier New", monospace;
  font-size: var(--note-code-font-size, 12px);
}

/* 代码块样式 */
.wysiwyg-editor .ProseMirror pre {
  background: rgba(0, 0, 0, 0.05);
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 8px 0;
  font-family: "SF Mono", "Monaco", "Menlo", "Roboto Mono", "Consolas",
    "Liberation Mono", "Courier New", monospace;
  font-size: var(--note-code-font-size, 12px);
}

.wysiwyg-editor .ProseMirror pre code {
  background: none;
  padding: 0;
  font-size: inherit;
}

/* 列表样式 */
.wysiwyg-editor .ProseMirror ul,
.wysiwyg-editor .ProseMirror ol {
  margin: 8px 0;
  padding-left: 20px;
}

.wysiwyg-editor .ProseMirror ul ul,
.wysiwyg-editor .ProseMirror ol ol,
.wysiwyg-editor .ProseMirror ul ol,
.wysiwyg-editor .ProseMirror ol ul {
  margin: 0;
}

.wysiwyg-editor .ProseMirror li {
  margin: 2px 0;
  line-height: 1.5;
}

.wysiwyg-editor .ProseMirror li p {
  margin: 0;
}

/* 任务列表样式 - 使用标准选择器和data属性 */
.wysiwyg-editor .ProseMirror ul[data-type="taskList"] {
  list-style: none;
  padding-left: 0;
}

.wysiwyg-editor .ProseMirror li[data-type="taskItem"] {
  display: flex;
  align-items: flex-start;
  margin: 4px 0;
}

.wysiwyg-editor .ProseMirror li[data-type="taskItem"] input[type="checkbox"] {
  margin-right: 8px;
  margin-top: 2px;
  cursor: pointer;
}

.wysiwyg-editor .ProseMirror li[data-type="taskItem"][data-checked="true"] {
  text-decoration: line-through;
  opacity: 0.7;
}

/* 引用样式 */
.wysiwyg-editor .ProseMirror blockquote {
  border-left: 3px solid currentColor;
  margin: 8px 0;
  padding-left: 12px;
  color: #6b7280;
  opacity: 0.8;
  font-style: italic;
}

/* 链接样式 - 使用标准选择器 */
.wysiwyg-editor .ProseMirror a {
  color: #3b82f6;
  text-decoration: underline;
  cursor: pointer;
}

.wysiwyg-editor .ProseMirror a:hover {
  color: #1d4ed8;
}

/* 图片样式 - 使用标准选择器 */
.wysiwyg-editor .ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 8px 0;
}

/* 分割线样式 */
.wysiwyg-editor .ProseMirror hr {
  border: none;
  height: 1px;
  background: rgba(0, 0, 0, 0.15);
  margin: 16px 0;
}

/* 表格样式 */
.wysiwyg-editor .ProseMirror table {
  border-collapse: collapse;
  width: 100%;
  font-size: var(--note-table-font-size, 12px);
  margin: 8px 0;
}

.wysiwyg-editor .ProseMirror table th,
.wysiwyg-editor .ProseMirror table td {
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 6px 8px;
  text-align: left;
  vertical-align: top;
}

.wysiwyg-editor .ProseMirror table th {
  background: rgba(0, 0, 0, 0.05);
  font-weight: 600;
}

/* 选中文本样式 */
.wysiwyg-editor .ProseMirror ::selection {
  background: rgba(59, 130, 246, 0.2);
}

/* 焦点样式 */
.wysiwyg-editor .ProseMirror:focus {
  outline: none;
}

/* 拖拽样式 */
.wysiwyg-editor .ProseMirror .ProseMirror-selectednode {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wysiwyg-editor {
    font-size: 12px;
  }

  .wysiwyg-editor .ProseMirror h1 {
    font-size: 16px;
  }

  .wysiwyg-editor .ProseMirror h2 {
    font-size: 14px;
  }

  .wysiwyg-editor .ProseMirror h3 {
    font-size: 13px;
  }
}

/* 流式光标动画 */
@keyframes cursorBlink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .wysiwyg-editor {
    color: #e5e7eb;
  }

  .wysiwyg-editor .ProseMirror.is-editor-empty::before {
    color: #6b7280;
  }

  .wysiwyg-editor .ProseMirror code {
    background: rgba(255, 255, 255, 0.1);
  }

  .wysiwyg-editor .ProseMirror pre {
    background: rgba(255, 255, 255, 0.05);
  }

  .wysiwyg-editor .ProseMirror blockquote {
    color: #9ca3af;
  }

  .wysiwyg-editor .ProseMirror .editor-link {
    color: #60a5fa;
  }

  .wysiwyg-editor .ProseMirror .editor-link:hover {
    color: #93c5fd;
  }

  .wysiwyg-editor .ProseMirror hr {
    background: rgba(255, 255, 255, 0.15);
  }

  /* ✅ 简化深色模式表格样式 */
  .wysiwyg-editor .ProseMirror table th,
  .wysiwyg-editor .ProseMirror table td {
    border-color: rgba(255, 255, 255, 0.1);
  }

  .wysiwyg-editor .ProseMirror table th {
    background: rgba(255, 255, 255, 0.05);
  }
}

/* ✅ 简化滚动条样式 - 使用浏览器默认样式，移除复杂的自定义样式 */
.wysiwyg-editor .ProseMirror,
.wysiwyg-editor .tiptap.ProseMirror {
  /* 使用浏览器原生滚动条，移除过度自定义 */
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

/* 编辑器健康状态样式 */
.wysiwyg-editor.editor-warning {
  border: 2px solid #f59e0b;
  background-color: #fffbeb;
}

.wysiwyg-editor.editor-error {
  border: 2px solid #ef4444;
  background-color: #fef2f2;
}

/* 健康状态指示器 */
.editor-health-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 12px;
}

.health-status {
  font-size: 14px;
}

.health-message {
  color: #6b7280;
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* ✅ 编辑模式控制样式 - 遵循TipTap最佳实践 */
/* 预览模式：通过CSS控制外观，而不是disabled属性 */
.wysiwyg-editor.viewing .ProseMirror {
  /* 在预览模式下降低交互性视觉提示 */
  cursor: text;
  background-color: transparent;
}

.wysiwyg-editor.viewing .ProseMirror:hover {
  /* 悬停时提示可点击 */
  background-color: rgba(0, 0, 0, 0.02);
}

/* 编辑模式：正常的编辑器样式 */
.wysiwyg-editor.editing .ProseMirror {
  cursor: text;
  background-color: rgba(255, 255, 255, 0.8);
}

/* 流式输入模式 */
.wysiwyg-editor.streaming .ProseMirror {
  background-color: rgba(59, 130, 246, 0.05);
  border-left: 3px solid #3b82f6;
}
