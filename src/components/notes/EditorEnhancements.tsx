import React, { useEffect, useRef, useCallback } from 'react';
import { Editor } from '@tiptap/react';
import { message } from 'antd';

/**
 * 编辑器增强功能组件属性
 */
interface EditorEnhancementsProps {
  /** 编辑器实例 */
  editor: Editor | null;
  /** 便签ID */
  noteId?: string;
  /** 是否启用自动保存 */
  enableAutoSave?: boolean;
  /** 自动保存间隔（毫秒） */
  autoSaveInterval?: number;
  /** 保存回调 */
  onSave?: (content: string) => void;
  /** 是否启用快捷键 */
  enableShortcuts?: boolean;
  /** 是否启用撤销重做 */
  enableUndoRedo?: boolean;
}

/**
 * 编辑器增强功能组件
 * 提供自动保存、快捷键、撤销重做等增强功能
 */
const EditorEnhancements: React.FC<EditorEnhancementsProps> = ({
  editor,
  noteId,
  enableAutoSave = true,
  autoSaveInterval = 5000, // 5秒自动保存
  onSave,
  enableShortcuts = true,
  enableUndoRedo = true,
}) => {
  const autoSaveTimerRef = useRef<NodeJS.Timeout | null>(null);
  const lastSavedContentRef = useRef<string>('');
  const isAutoSavingRef = useRef<boolean>(false);

  /**
   * 执行保存操作
   */
  const performSave = useCallback(
    (content: string, isAutoSave = false) => {
      if (!onSave || isAutoSavingRef.current) return;

      // 检查内容是否有变化
      if (content === lastSavedContentRef.current) return;

      try {
        isAutoSavingRef.current = true;
        onSave(content);
        lastSavedContentRef.current = content;

        if (!isAutoSave) {
          message.success('保存成功', 1);
        }
      } catch (error) {
        console.error('保存失败:', error);
        if (!isAutoSave) {
          message.error('保存失败');
        }
      } finally {
        isAutoSavingRef.current = false;
      }
    },
    [onSave]
  );

  /**
   * 自动保存功能
   */
  useEffect(() => {
    if (!enableAutoSave || !editor || !onSave) return;

    const startAutoSave = () => {
      if (autoSaveTimerRef.current) {
        clearInterval(autoSaveTimerRef.current);
      }

      autoSaveTimerRef.current = setInterval(() => {
        if (editor && !editor.isDestroyed) {
          const content = editor.getHTML();
          if (content && content.trim()) {
            performSave(content, true);
          }
        }
      }, autoSaveInterval);
    };

    startAutoSave();

    return () => {
      if (autoSaveTimerRef.current) {
        clearInterval(autoSaveTimerRef.current);
        autoSaveTimerRef.current = null;
      }
    };
  }, [editor, enableAutoSave, autoSaveInterval, performSave]);

  /**
   * 快捷键处理
   */
  useEffect(() => {
    if (!enableShortcuts || !editor) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + S: 手动保存
      if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault();
        if (editor && !editor.isDestroyed) {
          const content = editor.getHTML();
          performSave(content, false);
        }
        return;
      }

      // Ctrl/Cmd + Z: 撤销
      if (enableUndoRedo && (event.ctrlKey || event.metaKey) && event.key === 'z' && !event.shiftKey) {
        event.preventDefault();
        editor.chain().focus().undo().run();
        return;
      }

      // Ctrl/Cmd + Shift + Z 或 Ctrl/Cmd + Y: 重做
      if (
        enableUndoRedo &&
        (event.ctrlKey || event.metaKey) &&
        ((event.key === 'z' && event.shiftKey) || event.key === 'y')
      ) {
        event.preventDefault();
        editor.chain().focus().redo().run();
        return;
      }

      // Ctrl/Cmd + B: 粗体
      if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
        event.preventDefault();
        editor.chain().focus().toggleBold().run();
        return;
      }

      // Ctrl/Cmd + I: 斜体
      if ((event.ctrlKey || event.metaKey) && event.key === 'i') {
        event.preventDefault();
        editor.chain().focus().toggleItalic().run();
        return;
      }

      // Ctrl/Cmd + U: 下划线
      if ((event.ctrlKey || event.metaKey) && event.key === 'u') {
        event.preventDefault();
        editor.chain().focus().toggleStrike().run();
        return;
      }

      // Ctrl/Cmd + K: 行内代码
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        editor.chain().focus().toggleCode().run();
        return;
      }

      // Ctrl/Cmd + Shift + L: 无序列表
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'L') {
        event.preventDefault();
        editor.chain().focus().toggleBulletList().run();
        return;
      }

      // Ctrl/Cmd + Shift + O: 有序列表
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'O') {
        event.preventDefault();
        editor.chain().focus().toggleOrderedList().run();
        return;
      }

      // Ctrl/Cmd + Shift + T: 任务列表
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'T') {
        event.preventDefault();
        editor.chain().focus().toggleTaskList().run();
        return;
      }

      // Tab: 增加缩进
      if (event.key === 'Tab' && !event.shiftKey) {
        event.preventDefault();
        // 检查是否在列表中
        if (editor.isActive('listItem')) {
          editor.chain().focus().sinkListItem('listItem').run();
        } else {
          // 插入制表符或空格
          editor.chain().focus().insertContent('    ').run();
        }
        return;
      }

      // Shift + Tab: 减少缩进
      if (event.key === 'Tab' && event.shiftKey) {
        event.preventDefault();
        if (editor.isActive('listItem')) {
          editor.chain().focus().liftListItem('listItem').run();
        }
        return;
      }

      // Enter: 智能换行
      if (event.key === 'Enter') {
        // 在代码块中按 Shift+Enter 插入换行
        if (event.shiftKey && editor.isActive('codeBlock')) {
          event.preventDefault();
          editor.chain().focus().insertContent('\n').run();
          return;
        }
      }

      // Escape: 退出编辑模式（如果有相关处理）
      if (event.key === 'Escape') {
        // 这里可以添加退出编辑模式的逻辑
        editor.commands.blur();
        return;
      }
    };

    // 添加键盘事件监听
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [editor, enableShortcuts, enableUndoRedo, performSave]);

  /**
   * 编辑器焦点管理
   */
  useEffect(() => {
    if (!editor) return;

    const handleFocus = () => {
      // 编辑器获得焦点时的处理
      console.log(`📝 编辑器获得焦点: ${noteId}`);
    };

    const handleBlur = () => {
      // 编辑器失去焦点时自动保存
      if (enableAutoSave && onSave) {
        const content = editor.getHTML();
        if (content && content.trim()) {
          performSave(content, true);
        }
      }
      console.log(`📝 编辑器失去焦点: ${noteId}`);
    };

    editor.on('focus', handleFocus);
    editor.on('blur', handleBlur);

    return () => {
      editor.off('focus', handleFocus);
      editor.off('blur', handleBlur);
    };
  }, [editor, noteId, enableAutoSave, onSave, performSave]);

  /**
   * 内容变化监听
   */
  useEffect(() => {
    if (!editor) return;

    const handleUpdate = () => {
      // 内容更新时的处理
      // 这里可以添加实时保存逻辑或其他处理
    };

    editor.on('update', handleUpdate);

    return () => {
      editor.off('update', handleUpdate);
    };
  }, [editor]);

  // 这个组件不渲染任何UI，只提供功能增强
  return null;
};

export default EditorEnhancements;
