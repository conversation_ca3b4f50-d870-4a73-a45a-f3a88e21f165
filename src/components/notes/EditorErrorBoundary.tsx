import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button, Alert, Space } from 'antd';
import { ReloadOutlined, BugOutlined } from '@ant-design/icons';

/**
 * 编辑器错误边界组件属性
 */
interface EditorErrorBoundaryProps {
  children: ReactNode;
  /** 错误回调 */
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  /** 重置回调 */
  onReset?: () => void;
  /** 备用内容 */
  fallback?: ReactNode;
  /** 是否显示详细错误信息 */
  showDetails?: boolean;
}

/**
 * 编辑器错误边界状态
 */
interface EditorErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
  retryCount: number;
}

/**
 * 编辑器错误边界组件
 * 捕获编辑器相关错误，提供恢复机制
 */
export class EditorErrorBoundary extends Component<
  EditorErrorBoundaryProps,
  EditorErrorBoundaryState
> {
  private maxRetries = 3;
  private retryTimeout: NodeJS.Timeout | null = null;

  constructor(props: EditorErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<EditorErrorBoundaryState> {
    // 生成错误ID用于追踪
    const errorId = `editor_error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      hasError: true,
      error,
      errorId,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 更新状态
    this.setState({
      errorInfo,
    });

    // 记录错误
    this.logError(error, errorInfo);

    // 调用错误回调
    this.props.onError?.(error, errorInfo);

    // 尝试自动恢复
    this.attemptAutoRecovery(error);
  }

  /**
   * 记录错误信息
   */
  private logError(error: Error, errorInfo: ErrorInfo) {
    const { errorId } = this.state;
    
    console.group(`🚨 编辑器错误 [${errorId}]`);
    console.error('错误:', error);
    console.error('错误信息:', error.message);
    console.error('错误堆栈:', error.stack);
    console.error('组件堆栈:', errorInfo.componentStack);
    console.groupEnd();

    // 发送错误报告到监控服务（如果配置了）
    this.sendErrorReport(error, errorInfo);
  }

  /**
   * 发送错误报告
   */
  private sendErrorReport(error: Error, errorInfo: ErrorInfo) {
    // 这里可以集成错误监控服务，如 Sentry
    try {
      // 示例：发送到监控服务
      // errorReportingService.captureException(error, {
      //   tags: { component: 'EditorErrorBoundary' },
      //   extra: { errorInfo, errorId: this.state.errorId }
      // });
    } catch (reportError) {
      console.warn('发送错误报告失败:', reportError);
    }
  }

  /**
   * 尝试自动恢复
   */
  private attemptAutoRecovery(error: Error) {
    const { retryCount } = this.state;

    // 检查是否可以自动恢复
    if (this.canAutoRecover(error) && retryCount < this.maxRetries) {
      console.log(`🔄 尝试自动恢复编辑器 (${retryCount + 1}/${this.maxRetries})`);
      
      // 延迟重试，避免立即失败
      this.retryTimeout = setTimeout(() => {
        this.setState(prevState => ({
          hasError: false,
          error: null,
          errorInfo: null,
          retryCount: prevState.retryCount + 1,
        }));
      }, 1000 * (retryCount + 1)); // 递增延迟
    }
  }

  /**
   * 检查是否可以自动恢复
   */
  private canAutoRecover(error: Error): boolean {
    // 定义可以自动恢复的错误类型
    const recoverableErrors = [
      'ChunkLoadError', // 代码分割加载错误
      'NetworkError',   // 网络错误
      'TimeoutError',   // 超时错误
    ];

    return recoverableErrors.some(errorType => 
      error.name.includes(errorType) || error.message.includes(errorType)
    );
  }

  /**
   * 手动重置错误状态
   */
  private handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
    });

    // 调用重置回调
    this.props.onReset?.();
  };

  /**
   * 重新加载页面
   */
  private handleReload = () => {
    window.location.reload();
  };

  /**
   * 复制错误信息
   */
  private handleCopyError = () => {
    const { error, errorInfo, errorId } = this.state;
    const errorText = `
错误ID: ${errorId}
错误类型: ${error?.name}
错误信息: ${error?.message}
错误堆栈: ${error?.stack}
组件堆栈: ${errorInfo?.componentStack}
时间: ${new Date().toISOString()}
用户代理: ${navigator.userAgent}
    `.trim();

    navigator.clipboard.writeText(errorText).then(() => {
      console.log('错误信息已复制到剪贴板');
    }).catch(err => {
      console.warn('复制错误信息失败:', err);
    });
  };

  componentWillUnmount() {
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
    }
  }

  render() {
    const { hasError, error, retryCount } = this.state;
    const { children, fallback, showDetails = false } = this.props;

    if (hasError) {
      // 如果提供了自定义fallback，使用它
      if (fallback) {
        return fallback;
      }

      // 默认错误UI
      return (
        <div className="editor-error-boundary" style={{ padding: '20px' }}>
          <Alert
            message="编辑器遇到错误"
            description={
              <div>
                <p>编辑器组件发生了意外错误，请尝试以下操作：</p>
                {retryCount < this.maxRetries && (
                  <p>系统将在几秒后自动尝试恢复...</p>
                )}
                {showDetails && error && (
                  <details style={{ marginTop: '10px' }}>
                    <summary>错误详情</summary>
                    <pre style={{ 
                      background: '#f5f5f5', 
                      padding: '10px', 
                      borderRadius: '4px',
                      fontSize: '12px',
                      overflow: 'auto',
                      maxHeight: '200px'
                    }}>
                      {error.message}
                      {'\n\n'}
                      {error.stack}
                    </pre>
                  </details>
                )}
              </div>
            }
            type="error"
            showIcon
            action={
              <Space direction="vertical" size="small">
                <Button 
                  size="small" 
                  onClick={this.handleReset}
                  icon={<ReloadOutlined />}
                >
                  重试
                </Button>
                <Button 
                  size="small" 
                  onClick={this.handleReload}
                  type="primary"
                >
                  刷新页面
                </Button>
                {showDetails && (
                  <Button 
                    size="small" 
                    onClick={this.handleCopyError}
                    icon={<BugOutlined />}
                  >
                    复制错误信息
                  </Button>
                )}
              </Space>
            }
          />
        </div>
      );
    }

    return children;
  }
}

export default EditorErrorBoundary;
