import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import WysiwygEditor from '../WysiwygEditor';

// Mock TipTap editor
jest.mock('@tiptap/react', () => ({
  useEditor: jest.fn(() => ({
    getHTML: jest.fn(() => '<p>测试内容</p>'),
    commands: {
      setContent: jest.fn(),
      focus: jest.fn(),
    },
    on: jest.fn(),
    off: jest.fn(),
    destroy: jest.fn(),
    isDestroyed: false,
  })),
  EditorContent: ({ editor }: any) => <div data-testid="editor-content">编辑器内容</div>,
}));

// Mock extensions
jest.mock('@tiptap/starter-kit', () => ({
  __esModule: true,
  default: {},
}));

jest.mock('@tiptap/extension-placeholder', () => ({
  __esModule: true,
  default: {
    configure: jest.fn(() => ({})),
  },
}));

jest.mock('@tiptap/extension-task-list', () => ({
  __esModule: true,
  default: {},
}));

jest.mock('@tiptap/extension-task-item', () => ({
  __esModule: true,
  default: {
    configure: jest.fn(() => ({})),
  },
}));

// Mock utils
jest.mock('../../../utils/markdownConverter', () => ({
  cachedMarkdownToHtml: jest.fn((content) => `<p>${content}</p>`),
  cachedHtmlToMarkdown: jest.fn((content) => content.replace(/<[^>]*>/g, '')),
}));

jest.mock('../../../utils/editorBackup', () => ({
  editorBackupManager: {
    createBackup: jest.fn(),
    getStats: jest.fn(() => ({ totalBackups: 0 })),
  },
  createManualBackup: jest.fn(),
}));

jest.mock('../../../utils/editorPerformance', () => ({
  EditorPerformanceMonitor: jest.fn().mockImplementation(() => ({
    startMonitoring: jest.fn(),
    stopMonitoring: jest.fn(),
  })),
}));

// Mock CSS
jest.mock('../WysiwygEditor.css', () => ({}));

describe('WysiwygEditor', () => {
  const defaultProps = {
    content: '# 测试标题\n\n这是测试内容',
    onChange: jest.fn(),
    placeholder: '请输入内容...',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('应该正确渲染编辑器', () => {
    render(<WysiwygEditor {...defaultProps} />);
    
    expect(screen.getByTestId('editor-content')).toBeInTheDocument();
  });

  it('应该在启用备份时初始化性能监控', () => {
    const { EditorPerformanceMonitor } = require('../../../utils/editorPerformance');
    
    render(
      <WysiwygEditor
        {...defaultProps}
        noteId="test-note-1"
        enableBackup={true}
      />
    );

    expect(EditorPerformanceMonitor).toHaveBeenCalled();
  });

  it('应该在内容变化时调用 onChange', async () => {
    const onChange = jest.fn();
    
    render(
      <WysiwygEditor
        {...defaultProps}
        onChange={onChange}
      />
    );

    // 模拟内容变化
    // 注意：实际的 TipTap 编辑器交互需要更复杂的模拟
    // 这里只是基础的组件渲染测试
    expect(screen.getByTestId('editor-content')).toBeInTheDocument();
  });

  it('应该支持自动保存功能', () => {
    const onSave = jest.fn();
    
    render(
      <WysiwygEditor
        {...defaultProps}
        noteId="test-note-1"
        enableEnhancements={true}
        onSave={onSave}
      />
    );

    // 验证增强功能组件被渲染
    // 实际的自动保存测试需要更复杂的时间控制
    expect(screen.getByTestId('editor-content')).toBeInTheDocument();
  });

  it('应该在组件卸载时正确清理资源', () => {
    const { unmount } = render(
      <WysiwygEditor
        {...defaultProps}
        noteId="test-note-1"
        enableBackup={true}
      />
    );

    // 卸载组件
    unmount();

    // 验证清理逻辑被调用
    // 实际的清理验证需要监控相关的清理函数调用
  });

  it('应该处理错误边界', () => {
    // 模拟错误情况
    const consoleError = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    render(
      <WysiwygEditor
        {...defaultProps}
        noteId="test-note-1"
        enableBackup={true}
      />
    );

    consoleError.mockRestore();
  });

  it('应该支持流式内容更新', () => {
    render(
      <WysiwygEditor
        {...defaultProps}
        isStreaming={true}
      />
    );

    expect(screen.getByTestId('editor-content')).toBeInTheDocument();
  });

  it('应该正确应用样式类', () => {
    render(
      <WysiwygEditor
        {...defaultProps}
        className="custom-editor"
      />
    );

    const editorElement = screen.getByTestId('editor-content').parentElement;
    expect(editorElement).toHaveClass('wysiwyg-editor');
    expect(editorElement).toHaveClass('custom-editor');
  });

  it('应该支持禁用状态', () => {
    render(
      <WysiwygEditor
        {...defaultProps}
        disabled={true}
      />
    );

    expect(screen.getByTestId('editor-content')).toBeInTheDocument();
  });

  it('应该支持自动聚焦', () => {
    render(
      <WysiwygEditor
        {...defaultProps}
        autoFocus={true}
      />
    );

    expect(screen.getByTestId('editor-content')).toBeInTheDocument();
  });
});

describe('WysiwygEditor 集成测试', () => {
  it('应该完整地处理编辑流程', async () => {
    const onChange = jest.fn();
    const onSave = jest.fn();
    
    render(
      <WysiwygEditor
        content="初始内容"
        onChange={onChange}
        onSave={onSave}
        noteId="integration-test"
        enableBackup={true}
        enableEnhancements={true}
      />
    );

    // 验证编辑器正确渲染
    expect(screen.getByTestId('editor-content')).toBeInTheDocument();
    
    // 这里可以添加更多的集成测试逻辑
    // 例如模拟用户输入、快捷键操作等
  });

  it('应该正确处理错误恢复', () => {
    const onError = jest.fn();
    
    render(
      <WysiwygEditor
        content="测试内容"
        onChange={jest.fn()}
        noteId="error-test"
        enableBackup={true}
      />
    );

    // 模拟错误情况并验证恢复机制
    expect(screen.getByTestId('editor-content')).toBeInTheDocument();
  });
});

describe('WysiwygEditor 性能测试', () => {
  it('应该在大量内容时保持性能', () => {
    const largeContent = '# 大量内容\n\n' + '这是测试内容。'.repeat(1000);
    
    const startTime = performance.now();
    
    render(
      <WysiwygEditor
        content={largeContent}
        onChange={jest.fn()}
        noteId="performance-test"
      />
    );
    
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    // 验证渲染时间在合理范围内（这里设置为 100ms）
    expect(renderTime).toBeLessThan(100);
    expect(screen.getByTestId('editor-content')).toBeInTheDocument();
  });

  it('应该正确处理缓存', () => {
    const { cachedMarkdownToHtml } = require('../../../utils/markdownConverter');
    
    render(
      <WysiwygEditor
        content="缓存测试内容"
        onChange={jest.fn()}
      />
    );

    // 验证缓存函数被调用
    expect(cachedMarkdownToHtml).toHaveBeenCalled();
  });
});
