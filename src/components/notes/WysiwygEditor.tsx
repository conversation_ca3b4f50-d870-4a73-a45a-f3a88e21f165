import React, { useCallback, useEffect, useRef } from "react";
import { useEditor, EditorContent, type Editor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";
import Image from "@tiptap/extension-image";
import TaskList from "@tiptap/extension-task-list";
import TaskItem from "@tiptap/extension-task-item";
import { useCanvasStore } from "../../stores/canvasStore";
import {
  htmlToMarkdown,
  markdownToHtmlSync,
} from "../../utils/markdownConverter";
import "./WysiwygEditor.css";

// ✅ 简化编辑器配置 - 移除复杂的smartScroll配置

/**
 * 安全地执行编辑器命令，避免在编辑器未挂载时出错
 * 优化版本，增加类型安全
 */
const safeEditorCommand = (
  editor: Editor | null,
  command: (editor: Editor) => boolean | void,
  fallback?: () => void
): boolean => {
  if (editor && !editor.isDestroyed) {
    try {
      const result = command(editor);
      return result !== false;
    } catch (error) {
      console.warn("编辑器命令执行失败:", error);
      fallback?.();
      return false;
    }
  }
  fallback?.();
  return false;
};

/**
 * 所见即所得编辑器组件属性接口
 */
interface WysiwygEditorProps {
  /** 编辑器内容（Markdown格式） */
  content: string;
  /** 内容变化回调函数 */
  onChange: (content: string) => void;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否自动聚焦 */
  autoFocus?: boolean;
  /** 编辑器失焦回调 */
  onBlur?: () => void;
  /** 键盘事件回调 */
  onKeyDown?: (event: KeyboardEvent) => boolean;
  /** 是否禁用编辑器 */
  disabled?: boolean;
  /** 编辑器类名 */
  className?: string;
  /** 编辑器实例回调 */
  onEditorReady?: (editor: Editor) => void;
  /** 点击事件回调 */
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
  /** 鼠标按下事件回调 */
  onMouseDown?: (event: React.MouseEvent<HTMLDivElement>) => void;
  /** 内联样式 */
  style?: React.CSSProperties;
  /** 标题属性 */
  title?: string;
  /** 是否正在流式输入 */
  isStreaming?: boolean;
  // ✅ 移除config参数，简化配置
}

/**
 * 所见即所得编辑器组件
 * 基于TipTap实现，支持Markdown语法自动识别和转换
 */
const WysiwygEditor: React.FC<WysiwygEditorProps> = ({
  content,
  onChange,
  placeholder = "开始输入...",
  autoFocus = false,
  onBlur,
  onKeyDown,
  disabled = false,
  className = "",
  onEditorReady,
  onClick,
  onMouseDown,
  style,
  title,
  isStreaming = false,
  // ✅ 移除config参数，简化配置
}) => {
  // 引用和状态管理
  const editorRef = useRef<HTMLDivElement>(null);
  const proseMirrorRef = useRef<HTMLElement | null>(null);
  const viewReadyRef = useRef<boolean>(false); // 标记视图是否已准备好
  // ✅ 移除lastContentLengthRef，不再需要复杂的内容增长检测

  // 监听画布缩放状态
  const canvasScale = useCanvasStore((state) => state.scale);

  // ✅ 简化滚动逻辑 - 使用浏览器原生滚动，移除复杂的自定义检测
  const applyCanvasScale = useCallback(() => {
    if (proseMirrorRef.current && canvasScale !== 1) {
      proseMirrorRef.current.style.zoom = canvasScale.toString();
    } else if (proseMirrorRef.current) {
      proseMirrorRef.current.style.zoom = "";
    }
  }, [canvasScale]);

  // 初始化编辑器
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3, 4, 5, 6],
        },
        bulletList: {
          keepMarks: true,
          keepAttributes: true,
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: true,
        },
      }),
      Placeholder.configure({
        placeholder,
      }),
      Image.configure({
        HTMLAttributes: {
          class: "wysiwyg-image",
        },
      }),
      TaskList.configure({
        HTMLAttributes: {
          class: "task-list",
        },
      }),
      TaskItem.configure({
        HTMLAttributes: {
          class: "task-item",
        },
        nested: true,
      }),
    ],
    content: markdownToHtmlSync(content),
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      const markdown = htmlToMarkdown(html);
      onChange(markdown);
    },
    onFocus: () => {
      // 聚焦时的处理
    },
    onBlur: () => {
      onBlur?.();
    },
    onCreate: ({ editor }) => {
      // 编辑器创建完成后的初始化
      if (onEditorReady) {
        onEditorReady(editor);
      }

      // 设置ProseMirror引用
      if (editorRef.current) {
        const proseMirrorDiv = editorRef.current.querySelector(
          ".ProseMirror"
        ) as HTMLElement;
        if (proseMirrorDiv) {
          proseMirrorRef.current = proseMirrorDiv;
          viewReadyRef.current = true;
          // ✅ 简化初始化，移除复杂的滚动状态检测
          applyCanvasScale();
        }
      }

      // 如果需要自动聚焦
      if (autoFocus) {
        // 延迟聚焦，确保DOM已经完全渲染
        setTimeout(() => {
          safeEditorCommand(editor, (ed) => {
            ed.commands.focus("end");
          });
        }, 100);
      }
    },
    editorProps: {
      attributes: {
        class: `wysiwyg-editor-content ${className}`,
        title: title || "",
      },
      handleKeyDown: (_view, event) => {
        if (onKeyDown) {
          return onKeyDown(event);
        }
        return false;
      },
    },
  });

  // 处理内容变化
  useEffect(() => {
    if (editor && !editor.isDestroyed) {
      const currentMarkdown = htmlToMarkdown(editor.getHTML());
      if (currentMarkdown !== content) {
        const newHtml = markdownToHtmlSync(content);
        editor.commands.setContent(newHtml, { emitUpdate: false });
      }
    }
  }, [content, editor]);

  // ✅ 简化流式输入处理 - 移除复杂的智能滚动，使用浏览器原生滚动
  useEffect(() => {
    if (isStreaming && content) {
      // 简单的滚动到底部，让浏览器处理
      if (proseMirrorRef.current) {
        proseMirrorRef.current.scrollTop = proseMirrorRef.current.scrollHeight;
      }
    }
  }, [content, isStreaming]);

  // ✅ 简化画布缩放处理 - 直接应用缩放
  useEffect(() => {
    if (viewReadyRef.current) {
      applyCanvasScale();
    }
  }, [canvasScale, applyCanvasScale]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (editor) {
        editor.destroy();
      }
    };
  }, [editor]);

  return (
    <div
      ref={editorRef}
      className={`wysiwyg-editor ${className}`}
      style={style}
      onClick={onClick}
      onMouseDown={onMouseDown}
    >
      <EditorContent editor={editor} disabled={disabled} />
    </div>
  );
};

export { safeEditorCommand };
export default WysiwygEditor;
