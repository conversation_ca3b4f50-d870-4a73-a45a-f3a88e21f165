/**
 * 编辑器数据备份和恢复系统
 * 提供自动备份、手动备份、数据恢复等功能
 */

/**
 * 备份数据接口
 */
interface BackupData {
  id: string;
  noteId: string;
  content: string;
  timestamp: number;
  version: number;
  type: 'auto' | 'manual' | 'crash';
  metadata?: {
    userAgent?: string;
    url?: string;
    sessionId?: string;
  };
}

/**
 * 备份配置
 */
interface BackupConfig {
  /** 自动备份间隔（毫秒） */
  autoBackupInterval: number;
  /** 最大备份数量 */
  maxBackups: number;
  /** 备份存储键前缀 */
  storagePrefix: string;
  /** 是否启用崩溃检测 */
  enableCrashDetection: boolean;
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: BackupConfig = {
  autoBackupInterval: 30000, // 30秒
  maxBackups: 50,
  storagePrefix: 'editor_backup_',
  enableCrashDetection: true,
};

/**
 * 编辑器备份管理器
 */
export class EditorBackupManager {
  private config: BackupConfig;
  private autoBackupTimer: NodeJS.Timeout | null = null;
  private sessionId: string;
  private isActive = false;

  constructor(config: Partial<BackupConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.sessionId = this.generateSessionId();
    this.setupCrashDetection();
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 设置崩溃检测
   */
  private setupCrashDetection() {
    if (!this.config.enableCrashDetection) return;

    // 页面卸载时标记正常退出
    window.addEventListener('beforeunload', () => {
      localStorage.setItem('editor_session_ended', 'true');
    });

    // 页面加载时检查是否为异常退出
    window.addEventListener('load', () => {
      const sessionEnded = localStorage.getItem('editor_session_ended');
      if (!sessionEnded) {
        console.warn('检测到可能的异常退出，尝试恢复数据...');
        this.handleCrashRecovery();
      }
      localStorage.removeItem('editor_session_ended');
    });
  }

  /**
   * 处理崩溃恢复
   */
  private handleCrashRecovery() {
    const recentBackups = this.getRecentBackups(5);
    if (recentBackups.length > 0) {
      console.log(`发现 ${recentBackups.length} 个最近的备份，可用于恢复`);
      // 触发恢复事件
      window.dispatchEvent(new CustomEvent('editorCrashRecovery', {
        detail: { backups: recentBackups }
      }));
    }
  }

  /**
   * 启动自动备份
   */
  start() {
    if (this.isActive) return;
    
    this.isActive = true;
    this.autoBackupTimer = setInterval(() => {
      this.performAutoBackup();
    }, this.config.autoBackupInterval);

    console.log('编辑器自动备份已启动');
  }

  /**
   * 停止自动备份
   */
  stop() {
    if (!this.isActive) return;

    this.isActive = false;
    if (this.autoBackupTimer) {
      clearInterval(this.autoBackupTimer);
      this.autoBackupTimer = null;
    }

    console.log('编辑器自动备份已停止');
  }

  /**
   * 执行自动备份
   */
  private performAutoBackup() {
    // 获取当前活跃的编辑器内容
    const activeEditors = this.getActiveEditorContents();
    
    activeEditors.forEach(({ noteId, content }) => {
      if (content && content.trim()) {
        this.createBackup(noteId, content, 'auto');
      }
    });
  }

  /**
   * 获取活跃编辑器内容
   */
  private getActiveEditorContents(): Array<{ noteId: string; content: string }> {
    const results: Array<{ noteId: string; content: string }> = [];
    
    // 查找页面中的编辑器元素
    const editorElements = document.querySelectorAll('[data-editor-note-id]');
    
    editorElements.forEach(element => {
      const noteId = element.getAttribute('data-editor-note-id');
      const contentElement = element.querySelector('.ProseMirror');
      
      if (noteId && contentElement) {
        const content = contentElement.textContent || '';
        if (content.trim()) {
          results.push({ noteId, content });
        }
      }
    });

    return results;
  }

  /**
   * 创建备份
   */
  createBackup(noteId: string, content: string, type: BackupData['type'] = 'manual'): string {
    const backup: BackupData = {
      id: this.generateBackupId(),
      noteId,
      content,
      timestamp: Date.now(),
      version: this.getNextVersion(noteId),
      type,
      metadata: {
        userAgent: navigator.userAgent,
        url: window.location.href,
        sessionId: this.sessionId,
      },
    };

    // 保存备份
    this.saveBackup(backup);

    // 清理旧备份
    this.cleanupOldBackups();

    console.log(`创建备份: ${backup.id} (类型: ${type})`);
    return backup.id;
  }

  /**
   * 生成备份ID
   */
  private generateBackupId(): string {
    return `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取下一个版本号
   */
  private getNextVersion(noteId: string): number {
    const backups = this.getBackupsForNote(noteId);
    const maxVersion = Math.max(0, ...backups.map(b => b.version));
    return maxVersion + 1;
  }

  /**
   * 保存备份到本地存储
   */
  private saveBackup(backup: BackupData) {
    const key = `${this.config.storagePrefix}${backup.id}`;
    try {
      localStorage.setItem(key, JSON.stringify(backup));
    } catch (error) {
      console.warn('保存备份失败:', error);
      // 如果存储空间不足，清理一些旧备份后重试
      this.cleanupOldBackups(true);
      try {
        localStorage.setItem(key, JSON.stringify(backup));
      } catch (retryError) {
        console.error('重试保存备份仍然失败:', retryError);
      }
    }
  }

  /**
   * 获取所有备份
   */
  getAllBackups(): BackupData[] {
    const backups: BackupData[] = [];
    const prefix = this.config.storagePrefix;

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(prefix)) {
        try {
          const backup = JSON.parse(localStorage.getItem(key) || '');
          backups.push(backup);
        } catch (error) {
          console.warn(`解析备份数据失败: ${key}`, error);
        }
      }
    }

    return backups.sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * 获取指定便签的备份
   */
  getBackupsForNote(noteId: string): BackupData[] {
    return this.getAllBackups().filter(backup => backup.noteId === noteId);
  }

  /**
   * 获取最近的备份
   */
  getRecentBackups(limit: number = 10): BackupData[] {
    return this.getAllBackups().slice(0, limit);
  }

  /**
   * 恢复备份
   */
  restoreBackup(backupId: string): BackupData | null {
    const key = `${this.config.storagePrefix}${backupId}`;
    const backupData = localStorage.getItem(key);
    
    if (!backupData) {
      console.warn(`备份不存在: ${backupId}`);
      return null;
    }

    try {
      const backup: BackupData = JSON.parse(backupData);
      console.log(`恢复备份: ${backupId}`);
      return backup;
    } catch (error) {
      console.error(`恢复备份失败: ${backupId}`, error);
      return null;
    }
  }

  /**
   * 删除备份
   */
  deleteBackup(backupId: string): boolean {
    const key = `${this.config.storagePrefix}${backupId}`;
    try {
      localStorage.removeItem(key);
      console.log(`删除备份: ${backupId}`);
      return true;
    } catch (error) {
      console.error(`删除备份失败: ${backupId}`, error);
      return false;
    }
  }

  /**
   * 清理旧备份
   */
  private cleanupOldBackups(force = false) {
    const allBackups = this.getAllBackups();
    const maxBackups = force ? Math.floor(this.config.maxBackups * 0.7) : this.config.maxBackups;

    if (allBackups.length > maxBackups) {
      const toDelete = allBackups.slice(maxBackups);
      toDelete.forEach(backup => {
        this.deleteBackup(backup.id);
      });
      console.log(`清理了 ${toDelete.length} 个旧备份`);
    }
  }

  /**
   * 获取备份统计信息
   */
  getStats() {
    const allBackups = this.getAllBackups();
    const totalSize = allBackups.reduce((size, backup) => 
      size + JSON.stringify(backup).length, 0
    );

    return {
      totalBackups: allBackups.length,
      totalSize,
      autoBackups: allBackups.filter(b => b.type === 'auto').length,
      manualBackups: allBackups.filter(b => b.type === 'manual').length,
      crashBackups: allBackups.filter(b => b.type === 'crash').length,
      oldestBackup: allBackups[allBackups.length - 1]?.timestamp,
      newestBackup: allBackups[0]?.timestamp,
    };
  }

  /**
   * 清理所有备份
   */
  clearAllBackups() {
    const allBackups = this.getAllBackups();
    allBackups.forEach(backup => {
      this.deleteBackup(backup.id);
    });
    console.log(`清理了所有 ${allBackups.length} 个备份`);
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.stop();
    console.log('编辑器备份管理器已销毁');
  }
}

/**
 * 全局备份管理器实例
 */
export const editorBackupManager = new EditorBackupManager();

/**
 * 便捷函数：创建手动备份
 */
export function createManualBackup(noteId: string, content: string): string {
  return editorBackupManager.createBackup(noteId, content, 'manual');
}

/**
 * 便捷函数：恢复备份
 */
export function restoreBackup(backupId: string): BackupData | null {
  return editorBackupManager.restoreBackup(backupId);
}

/**
 * 便捷函数：获取便签的备份历史
 */
export function getBackupHistory(noteId: string): BackupData[] {
  return editorBackupManager.getBackupsForNote(noteId);
}
