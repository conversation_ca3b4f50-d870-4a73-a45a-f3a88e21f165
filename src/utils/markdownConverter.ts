/**
 * 优化的Markdown转换工具
 * 使用成熟的第三方库替代复杂的自定义转换逻辑
 * 添加智能缓存和性能优化
 */

import TurndownService from "turndown";
import { unified } from "unified";
import remarkParse from "remark-parse";
import remarkHtml from "remark-html";
import remarkGfm from "remark-gfm";

/**
 * 转换缓存配置
 */
const CACHE_CONFIG = {
  maxSize: 200, // 最大缓存条目数
  maxAge: 5 * 60 * 1000, // 5分钟过期时间
  cleanupInterval: 60 * 1000, // 1分钟清理一次
};

/**
 * 缓存条目接口
 */
interface CacheEntry {
  value: string;
  timestamp: number;
  accessCount: number;
}

/**
 * 智能缓存类
 */
class SmartCache {
  private cache = new Map<string, CacheEntry>();
  private cleanupTimer: NodeJS.Timeout | null = null;

  constructor() {
    this.startCleanup();
  }

  get(key: string): string | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    // 检查是否过期
    if (Date.now() - entry.timestamp > CACHE_CONFIG.maxAge) {
      this.cache.delete(key);
      return null;
    }

    // 更新访问计数
    entry.accessCount++;
    return entry.value;
  }

  set(key: string, value: string): void {
    // 如果缓存已满，清理最少使用的条目
    if (this.cache.size >= CACHE_CONFIG.maxSize) {
      this.evictLeastUsed();
    }

    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      accessCount: 1,
    });
  }

  private evictLeastUsed(): void {
    let leastUsedKey = "";
    let leastUsedCount = Infinity;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.accessCount < leastUsedCount) {
        leastUsedCount = entry.accessCount;
        leastUsedKey = key;
      }
    }

    if (leastUsedKey) {
      this.cache.delete(leastUsedKey);
    }
  }

  private startCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      const now = Date.now();
      for (const [key, entry] of this.cache.entries()) {
        if (now - entry.timestamp > CACHE_CONFIG.maxAge) {
          this.cache.delete(key);
        }
      }
    }, CACHE_CONFIG.cleanupInterval);
  }

  clear(): void {
    this.cache.clear();
  }

  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    this.clear();
  }

  getStats() {
    return {
      size: this.cache.size,
      maxSize: CACHE_CONFIG.maxSize,
      hitRate: this.calculateHitRate(),
    };
  }

  private calculateHitRate(): number {
    const totalAccess = Array.from(this.cache.values()).reduce(
      (sum, entry) => sum + entry.accessCount,
      0
    );
    return totalAccess > 0 ? (this.cache.size / totalAccess) * 100 : 0;
  }
}

/**
 * 全局缓存实例
 */
const conversionCache = new SmartCache();

// HTML to Markdown 转换器
const turndownService = new TurndownService({
  headingStyle: "atx",
  bulletListMarker: "-",
  codeBlockStyle: "fenced",
  fence: "```",
  emDelimiter: "*",
  strongDelimiter: "**",
  linkStyle: "inlined",
  linkReferenceStyle: "full",
});

// 配置转换规则
turndownService.addRule("taskList", {
  filter: (node: any) => {
    return (
      node.nodeName === "LI" &&
      node.parentNode &&
      node.parentNode.nodeName === "UL" &&
      node.firstChild &&
      node.firstChild.nodeName === "INPUT" &&
      node.firstChild.type === "checkbox"
    );
  },
  replacement: (content: string, node: any) => {
    const checkbox = node.querySelector('input[type="checkbox"]');
    const checked = checkbox && checkbox.checked;
    const prefix = checked ? "- [x] " : "- [ ] ";
    return prefix + content.trim() + "\n";
  },
});

turndownService.addRule("images", {
  filter: "img",
  replacement: (_content: string, node: any) => {
    const alt = node.alt || "";
    const src = node.src || "";
    const title = node.title || "";
    const titlePart = title ? ` "${title}"` : "";
    return `![${alt}](${src}${titlePart})`;
  },
});

// Markdown to HTML 处理器
const markdownProcessor = unified()
  .use(remarkParse)
  .use(remarkGfm)
  .use(remarkHtml, { sanitize: false });

/**
 * HTML转Markdown - 优化版本，使用智能缓存
 */
export function htmlToMarkdown(html: string): string {
  if (!html || html.trim() === "") {
    return "";
  }

  // 生成缓存键
  const cacheKey = `html2md:${html}`;

  // 尝试从缓存获取
  const cached = conversionCache.get(cacheKey);
  if (cached !== null) {
    return cached;
  }

  try {
    const result = turndownService.turndown(html);
    // 缓存结果
    conversionCache.set(cacheKey, result);
    return result;
  } catch (error) {
    console.warn("HTML转换为Markdown失败:", error);
    return html; // 失败时返回原始HTML
  }
}

/**
 * 同步版本的Markdown转HTML - 优化版本，使用智能缓存
 */
export function markdownToHtmlSync(markdown: string): string {
  if (!markdown || markdown.trim() === "") {
    return "";
  }

  // 生成缓存键
  const cacheKey = `md2html:${markdown}`;

  // 尝试从缓存获取
  const cached = conversionCache.get(cacheKey);
  if (cached !== null) {
    return cached;
  }

  try {
    const result = markdownProcessor.processSync(markdown);
    const html = String(result);
    // 缓存结果
    conversionCache.set(cacheKey, html);
    return html;
  } catch (error) {
    console.warn("Markdown转换为HTML失败:", error);
    return markdown;
  }
}

/**
 * 异步版本的Markdown转HTML - 优化版本，使用智能缓存
 */
export async function markdownToHtml(markdown: string): Promise<string> {
  if (!markdown || markdown.trim() === "") {
    return "";
  }

  // 生成缓存键
  const cacheKey = `md2html:${markdown}`;

  // 尝试从缓存获取
  const cached = conversionCache.get(cacheKey);
  if (cached !== null) {
    return cached;
  }

  try {
    const result = await markdownProcessor.process(markdown);
    const html = String(result);
    // 缓存结果
    conversionCache.set(cacheKey, html);
    return html;
  } catch (error) {
    console.warn("Markdown转换为HTML失败:", error);
    return markdown; // 失败时返回原始Markdown
  }
}

/**
 * 获取转换缓存统计信息
 */
export function getConversionCacheStats() {
  return conversionCache.getStats();
}

/**
 * 清理转换缓存
 */
export function clearConversionCache() {
  conversionCache.clear();
}

/**
 * 销毁转换器（在应用卸载时调用）
 */
export function destroyConverter() {
  conversionCache.destroy();
}

/**
 * 预热缓存 - 预先转换常用内容
 */
export function warmupCache(commonContents: string[]) {
  commonContents.forEach((content) => {
    if (content.trim()) {
      // 预转换常用内容
      markdownToHtmlSync(content);
      htmlToMarkdown(content);
    }
  });
}

/**
 * 批量转换 - 优化大量内容转换的性能
 */
export function batchMarkdownToHtml(markdowns: string[]): string[] {
  return markdowns.map((md) => markdownToHtmlSync(md));
}

/**
 * 批量转换 - HTML 到 Markdown
 */
export function batchHtmlToMarkdown(htmls: string[]): string[] {
  return htmls.map((html) => htmlToMarkdown(html));
}

/**
 * 检查内容是否为HTML格式
 */
export function isHtmlContent(content: string): boolean {
  if (!content) return false;

  // 简单的HTML检测：包含HTML标签
  const htmlTagRegex = /<[^>]+>/;
  return htmlTagRegex.test(content.trim());
}

/**
 * 检查内容是否为Markdown格式
 */
export function isMarkdownContent(content: string): boolean {
  if (!content) return false;

  // 简单的Markdown检测
  const markdownPatterns = [
    /^#+ /m, // 标题
    /^\* |\*\*|__/m, // 粗体斜体
    /^\- |\+ |\d+\. /m, // 列表
    /\[.*\]\(.*\)/, // 链接
    /!\[.*\]\(.*\)/, // 图片
    /```|`/, // 代码
    /^\> /m, // 引用
  ];

  return markdownPatterns.some((pattern) => pattern.test(content));
}
