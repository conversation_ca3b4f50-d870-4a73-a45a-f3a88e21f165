/**
 * 简化的Markdown转换工具
 * 使用成熟的第三方库替代复杂的自定义转换逻辑
 */

import TurndownService from "turndown";
import { unified } from "unified";
import remarkParse from "remark-parse";
import remarkHtml from "remark-html";
import remarkGfm from "remark-gfm";

// HTML to Markdown 转换器
const turndownService = new TurndownService({
  headingStyle: "atx",
  bulletListMarker: "-",
  codeBlockStyle: "fenced",
  fence: "```",
  emDelimiter: "*",
  strongDelimiter: "**",
  linkStyle: "inlined",
  linkReferenceStyle: "full",
});

// 配置转换规则
turndownService.addRule("taskList", {
  filter: (node: any) => {
    return (
      node.nodeName === "LI" &&
      node.parentNode &&
      node.parentNode.nodeName === "UL" &&
      node.firstChild &&
      node.firstChild.nodeName === "INPUT" &&
      node.firstChild.type === "checkbox"
    );
  },
  replacement: (content: string, node: any) => {
    const checkbox = node.querySelector('input[type="checkbox"]');
    const checked = checkbox && checkbox.checked;
    const prefix = checked ? "- [x] " : "- [ ] ";
    return prefix + content.trim() + "\n";
  },
});

turndownService.addRule("images", {
  filter: "img",
  replacement: (_content: string, node: any) => {
    const alt = node.alt || "";
    const src = node.src || "";
    const title = node.title || "";
    const titlePart = title ? ` "${title}"` : "";
    return `![${alt}](${src}${titlePart})`;
  },
});

// Markdown to HTML 处理器
const markdownProcessor = unified()
  .use(remarkParse)
  .use(remarkGfm)
  .use(remarkHtml, { sanitize: false });

/**
 * HTML转Markdown
 */
export function htmlToMarkdown(html: string): string {
  if (!html || html.trim() === "") {
    return "";
  }

  try {
    return turndownService.turndown(html);
  } catch (error) {
    console.warn("HTML转换为Markdown失败:", error);
    return html; // 失败时返回原始HTML
  }
}

/**
 * Markdown转HTML
 */
export async function markdownToHtml(markdown: string): Promise<string> {
  if (!markdown || markdown.trim() === "") {
    return "";
  }

  try {
    const result = await markdownProcessor.process(markdown);
    return String(result);
  } catch (error) {
    console.warn("Markdown转换为HTML失败:", error);
    return markdown; // 失败时返回原始Markdown
  }
}

/**
 * 同步版本的Markdown转HTML (用于兼容现有代码)
 */
export function markdownToHtmlSync(markdown: string): string {
  if (!markdown || markdown.trim() === "") {
    return "";
  }

  try {
    const result = markdownProcessor.processSync(markdown);
    return String(result);
  } catch (error) {
    console.warn("Markdown转换为HTML失败:", error);
    return markdown;
  }
}

/**
 * 检查内容是否为HTML格式
 */
export function isHtmlContent(content: string): boolean {
  if (!content) return false;

  // 简单的HTML检测：包含HTML标签
  const htmlTagRegex = /<[^>]+>/;
  return htmlTagRegex.test(content.trim());
}

/**
 * 检查内容是否为Markdown格式
 */
export function isMarkdownContent(content: string): boolean {
  if (!content) return false;

  // 简单的Markdown检测
  const markdownPatterns = [
    /^#+ /m, // 标题
    /^\* |\*\*|__/m, // 粗体斜体
    /^\- |\+ |\d+\. /m, // 列表
    /\[.*\]\(.*\)/, // 链接
    /!\[.*\]\(.*\)/, // 图片
    /```|`/, // 代码
    /^\> /m, // 引用
  ];

  return markdownPatterns.some((pattern) => pattern.test(content));
}
